import { PromptTemplate, SystemMessagePromptTemplate } from '@langchain/core/prompts'
import { PrismaMongoClient } from 'model/mongodb/prisma'
import { ITask, TaskStatus } from './type'
import logger from 'model/logger/logger'
import { LLM } from 'lib/ai/llm/llm_model'
import { ChatHistoryService } from '../chat_history/chat_history'


export interface ICreateTask {
  description: string
  sendTime?: string
  type?: 'fixed_time' | 'flexible'
}

/**
 * 极简任务管理器
 * 只保留4个核心动作：批量创建任务、查活跃任务、改状态、改优先级
 * 删除 = 软删除（status 设为 CANCELED）
 */
export class TaskManager {

  /**
   * 批量创建任务
   * @param chat_id 聊天ID
   * @param task_descriptions
   * @param overall_goal 总体目标
   * @param round_id 轮次ID（可选）
   * @returns 创建的任务数组
   */
  static async createTasks(
    chat_id: string,
    task_descriptions: ICreateTask[],
    overall_goal: string,
    round_id?: string
  ) {
    const prismaClient = PrismaMongoClient.getInstance()

    // 使用 Promise.all 并行创建任务，避免 createMany 的事务问题
    const tasks = await Promise.all(
      task_descriptions.map((description, index) =>
        prismaClient.task.create({
          data: {
            chat_id,
            overall_goal,
            description: description.description,
            send_time: description.sendTime,
            type: description.type,
            round_id,
            priority: index  // 同一批任务按下标排优先级
          }
        })
      )
    )

    return { count: tasks.length, tasks }
  }

  /**
   * 拉取活跃任务，按优先级 → 创建时间排序
   * @param chat_id 聊天ID
   * @returns 活跃任务列表
   */
  static async getActiveTasks(chat_id: string) : Promise<ITask[]> {
    const prismaClient = PrismaMongoClient.getInstance()

    return prismaClient.task.findMany({
      where: {
        chat_id,
        status: { in: [TaskStatus.TODO ] },
      },
      orderBy: [
        { priority: 'asc' },
        { created_at: 'asc' }
      ]
    })
  }

  static async getFlexibleActiveTasks(chat_id: string) : Promise<ITask[]> {
    const prismaClient = PrismaMongoClient.getInstance()

    return prismaClient.task.findMany({
      where: {
        chat_id,
        status: { in: [TaskStatus.TODO ] },
        OR: [
          { type: 'flexible' },
          { type: null }
        ],
        send_time: { not: null }
      },
      orderBy: [
        { priority: 'asc' },
        { created_at: 'asc' }
      ]
    })
  }

  static async getScheduledTasks(chat_id: string) : Promise<ITask[]> {
    const prismaClient = PrismaMongoClient.getInstance()

    return prismaClient.task.findMany({
      where: {
        chat_id,
        status: { in: [TaskStatus.TODO ] },
        send_time: { not: null }
      },
      orderBy: [
        { priority: 'asc' },
        { created_at: 'asc' }
      ]
    })
  }

  /**
   * 拉取活跃任务，按优先级 → 创建时间排序
   * @param chat_id 聊天ID
   * @returns 活跃任务列表
   */
  static async getStringifyActiveTasks(chat_id: string) : Promise<string> {
    const tasks =  await this.getFlexibleActiveTasks(chat_id)
    return tasks.map((task, index) => `${index + 1}. ${task.description}`).join('\n')
  }

  /**
   * 特殊快捷：取消任务（软删除）
   * @param task_id 任务ID
   */
  static async cancelTask(task_id: string) {
    return this.updateStatus(task_id, TaskStatus.CANCELED)
  }


  /**
   * 任务通用更新：只改动你给出的字段
   *    - 支持 priority、status、description、overall_goal
   *    - status = DONE 时自动写入 completed_at
   */
  static async updateTask(
    task_id: string,
    patch: {
        priority?: number
        status?: TaskStatus
        description?: string
        overall_goal?: string
        send_time?: string
        type?: string
      }
  ) {
    // 防御性编程：检查 task_id 是否有效
    if (!task_id || typeof task_id !== 'string') {
      logger.error(`Invalid task_id: ${task_id}`)
      return
    }

    const prismaClient = PrismaMongoClient.getInstance()

    return prismaClient.task.update({
      where: { id: task_id },
      data: {
        ...patch,
        completed_at: patch.status === TaskStatus.DONE ? new Date() : undefined
      }
    })
  }


  static async updateStatus(task_id: string, status: TaskStatus) {
    return this.updateTask(task_id, { status })
  }

  /*
   * newPriority 越小越重要
   */
  static async updatePriority(task_id: string, newPriority: number) {
    return this.updateTask(task_id, { priority: newPriority })
  }

  static async getTaskById(task_id: string) {
    const prismaClient = PrismaMongoClient.getInstance()
    return prismaClient.task.findUnique({
      where: {
        id: task_id
      }
    })
  }

  /**
   * 批量更新任务描述
   * @param updates 更新数组，包含任务ID和新描述
   */
  static async updateTaskDescriptions(updates: Array<{ task_id: string, description: string }>) {
    const prismaClient = PrismaMongoClient.getInstance()

    // 过滤出有效的更新请求
    const validUpdates = updates.filter(({ task_id }) => {
      if (!task_id || typeof task_id !== 'string') {
        console.warn(`Invalid task_id in updateTaskDescriptions: ${task_id}`)
        return false
      }
      return true
    })

    // 使用 Promise.all 并行更新任务
    const results = await Promise.all(
      validUpdates.map(({ task_id, description }) =>
        prismaClient.task.update({
          where: { id: task_id },
          data: { description }
        })
      )
    )

    return results
  }

  /**
   * 批量取消任务（软删除）
   * @param task_ids 任务ID数组
   */
  static async cancelTasks(task_ids: string[]) {
    // 过滤出有效的任务ID
    const validTaskIds = task_ids.filter((task_id) => {
      if (!task_id || typeof task_id !== 'string') {
        console.warn(`Invalid task_id in cancelTasks: ${task_id}`)
        return false
      }
      return true
    })

    // 使用 Promise.all 并行取消任务
    return await Promise.all(
      validTaskIds.map((task_id) =>
        this.updateStatus(task_id, TaskStatus.CANCELED)
      )
    )
  }

  static async mergeTasks(chat_id: string, toAddTasks: {id: string; description: string}[], tasks: ITask[], round_id?: string) {
    const prompt = PromptTemplate.fromTemplate(`你要对给出的任务进行精简，仅删除与合并。保证已有+新增的总数量 < {{limit}}。

**输入**
今日已有任务数组:
{{existing_task}}

要添加的任务:
{{to_add_tasks}}

任务总数：{{total}}

字段说明: 
  * \`id\`: 字符串
  * \`content\`: 文本
  * \`send_time\`: "HH\\:MM" 或空

**允许的操作**

1. **删除**：将冗余/违规/被合并掉的任务列入 \`toRemove\`。
2. **合并**：仅通过**指定目标任务的合并结果文案**完成，写入 \`toMerge\`：

   * 结构：\`{ "from": [被合并ID…], "into": "目标ID", "mergedContent": "合并后的最终文案" }\`
   * 合并后系统应以 \`mergedContent\` 覆盖 \`into\` 的 \`content\`。

> 禁止新增任务；不得输出除 \`toMerge\`、\`toRemove\` 外的其他变更指令。

**合规与压缩规则（按顺序执行）**

1. **合规先行**（先删后并）：

   * 删除第三天课（周三）21:00 前任何“直接销售”导向内容。
   * 若“小讲堂阶段”或“上课阶段”任务在客户**已看完小讲堂**或**已完成当天课程**的前提下重复触达，删除重复提醒。
2. **去重与近似合并**：

   * 若 \`content\` 相同，或“意图相同且时间窗口重叠（同一堂课前/后 ±30 分钟）”，合并为一条。
   * 合并规范：

     * 选定一条**存活任务**作为 \`into\`，其 \`id\`、\`type\`、\`send_time\` **不得改变**；
     * 将 \`from\` 中所有被合并任务的 \`id\` 记入 \`mergedContent\` 末尾：\`【合并自: ID1,ID2】\`；
     * 所有 \`from\` 的 \`id\` 必须同时进入 \`toRemove\`。
3. **优先级裁剪**（当各类型仍超上限时）：
   * 保留顺序：
到课提醒 > 课后回访/作业收集 > 开营/加播提醒 > 资料/音频发放 > 解读回复 > 针对性问题跟进 > 体验反馈收集 > 资料查收确认 > 轻问候
4. **硬性上限**：

   * 按第 3 点继续裁剪直至任务数量小于 {{limit}}。

**不可更改的内容**

* 不改变任何 \`into\` 任务的 \`type\`、\`send_time\`、\`id\`；仅允许以 \`mergedContent\` 覆盖其 \`content\`。
* 未涉及的任务无需输出。

**输出（只输出 JSON）**

{
  "think": "（用一句话说明本次删除与合并的核心依据）",
  "plans": {
    "toMerge": [
      {
        "from": ["（需要合并的任务ID1）", "（需要合并的任务ID2）", "..."],
        "into": "（合并到的目标任务ID）",
        "mergedContent": "（合并后的任务）"
      }
    ],
    "toRemove": [
      "（删除或被合并掉的任务ID…）"
    ]
  }
}`, { templateFormat: 'mustache' })

    const existing_task = tasks.map((task) => {
      return {
        id: task.id.slice(-4),
        content: task.description
      }
    })

    const to_add_tasks = toAddTasks.map((task) => {
      return {
        id: task.id,
        content: task.description
      }
    })

    const total = tasks.length + toAddTasks.length
    const limit = 5 // 设置任务数量上限

    const result = await LLM.predict(prompt, {
      model: 'gpt-5-mini',
      promptName: 'mergePlan',
      meta: { chat_id, round_id },
      responseJSON: true,
    }, {
      existing_task: JSON.stringify(existing_task),
      to_add_tasks: JSON.stringify(to_add_tasks),
      total,
      limit
    })

    // 解析 JSON 结果
    let parsedResult
    try {
      parsedResult = JSON.parse(result)
    } catch (error) {
      logger.error('Failed to parse LLM result:', error)
      return toAddTasks // 解析失败时返回原数组
    }

    const { plans } = parsedResult
    if (!plans) {
      logger.error('No plans found in LLM result')
      return toAddTasks
    }

    // 建立 ID 映射关系：slice(-4) -> 完整ID
    const idMapping = new Map<string, string>()
    tasks.forEach((task) => {
      const slicedId = task.id.slice(-4)
      idMapping.set(slicedId, task.id)
    })

    // 建立 toAddTasks 的 ID 映射
    const toAddTasksMap = new Map<string, number>()
    toAddTasks.forEach((task, index) => {
      toAddTasksMap.set(task.id, index)
    })

    // 处理 toMerge 操作
    const { toMerge = [], toRemove = [] } = plans
    const allToRemove = new Set<string>([...toRemove])

    // 处理合并操作
    for (const mergeOp of toMerge) {
      const { from, into, mergedContent } = mergeOp

      // 将被合并的任务添加到删除列表
      from.forEach((id: string) => allToRemove.add(id))

      // 更新目标任务的描述
      if (idMapping.has(into)) {
        // 目标任务在 existing tasks 中
        const fullId = idMapping.get(into)!
        await this.updateTask(fullId, { description: mergedContent })
      } else if (toAddTasksMap.has(into)) {
        // 目标任务在 toAddTasks 中
        const index = toAddTasksMap.get(into)!
        toAddTasks[index].description = mergedContent
      }
    }

    // 处理删除操作
    const existingTasksToRemove: string[] = []
    const toAddTasksToRemove: string[] = []

    allToRemove.forEach((id) => {
      if (idMapping.has(id)) {
        // 在 existing tasks 中
        existingTasksToRemove.push(idMapping.get(id)!)
      } else if (toAddTasksMap.has(id)) {
        // 在 toAddTasks 中
        toAddTasksToRemove.push(id)
      }
    })

    // 批量取消 existing tasks
    if (existingTasksToRemove.length > 0) {
      await this.cancelTasks(existingTasksToRemove)
    }

    // 从 toAddTasks 中移除指定的任务
    return toAddTasks.filter((task) => !toAddTasksToRemove.includes(task.id))
  }

  /**
   * 异步检查并标记任务完成状态
   * 不阻塞主流程，在后台执行任务完成校验
   * @param chat_id 聊天ID
   * @param round_id
   * @param chatHistoryService
   */
  static async checkAndMarkTasksCompleted(
    chat_id: string,
    round_id: string,
    chatHistoryService: ChatHistoryService
  ): Promise<void> {
    const tasks = await TaskManager.getFlexibleActiveTasks(chat_id)

    // 检查规划完成情况并更新
    if (tasks) {
      const dialogHistory = await chatHistoryService.getChatHistory(chat_id, 5, 10)

      const tasksToSchedule = tasks.map((task) => {
        return {
          task_id: task.id.slice(-4),
          task_description: task.description
        }
      })

      const taskCheckPrompt = SystemMessagePromptTemplate.fromTemplate(`# 规划检查
- 你是一个规划完成情况检查助手

# 主要任务
- 思考（think）请根据对话历史判断当前规划是否完成，即使实际内容没有完成，只要完成语义传递就是完成。
- 答案（answer）仅输出完成的任务ID数组

# 对话历史
{{dialogHistory}}

# 当前规划
${tasksToSchedule.map((t) => `规划ID: "${t.task_id}", 描述：${t.task_description}`).join('\n')}

# 输出要求
请严格按照如下 JSON 格式输出
{
  "think": "（深度思考内容）",
  "answer": [(需要标记的规划ID)]
}`, { templateFormat: 'mustache' })

      const output = await LLM.predict(
        taskCheckPrompt,
        {
          model: 'gpt-5-mini',
          responseJSON: true,
          meta: { promptName: 'check_completed_tasks', chat_id: chat_id, round_id: round_id }
        },
        { dialogHistory: dialogHistory }
      )

      let answer: string[] = []

      try {
        const parsedOutput = JSON.parse(output)
        answer = parsedOutput.answer
      } catch (error) {
        logger.error('check_completed_tasks 解析 JSON 失败:', error)
      }

      if (answer.length > 0) {
        logger.log({ chat_id: chat_id }, `完成任务: ${answer.join(', ')}`)

        // 对 task Id 进行还原
        const idMap = tasks.reduce((map, task) => {
          map[task.id.slice(-4)] = task.id
          return map
        }, {} as Record<string, string>)

        // 过滤出有效的任务ID，避免传入undefined导致Prisma错误
        const validTaskIds = answer
          .map((id) => idMap[id])
          .filter((taskId) => taskId !== undefined)

        if (validTaskIds.length > 0) {
          await Promise.all(
            validTaskIds.map((taskId) => TaskManager.updateStatus(taskId, TaskStatus.DONE))
          )
        }
      }
    }
  }
}
