import { SilentReAsk } from 'service/schedule/silent_requestion'
import { AsyncLock } from 'model/lock/lock'
import { IChattingFlag } from '../state/user_flags'
import dayjs from 'dayjs'
import RateLimiter from 'model/redis/rate_limiter'
import { DataService } from '../helper/getter/get_data'
import { getState } from 'service/llm/state'
import { firstAskIntention, isInClassTime, startAskIntention } from '../client/event_handler'
import { IEventType } from 'model/logger/data_driven'
import { HumanTransfer, HumanTransferType } from '../human_transfer/human_transfer'
import { getUserId } from 'config/chat_id'
import { Homework1Store } from '../workflow/nodes/homework1'
import { Homework2Store } from '../workflow/nodes/homework2'
import { TaskName } from './type'
import logger from 'model/logger/logger'
import { randomSleep } from 'lib/schedule/schedule'
import { ContextBuilder } from '../workflow/context'
import { replyClient, } from '../service/instance'
import { SendMessageType } from 'service/visualized_sop/common_sender/type'
import { Config } from 'config'
import { chatDBClient, chatHistoryServiceClient, chatStateStoreClient } from '../service/base_instance'
import { wecomCommonMessageSender, wecomMessageSender } from '../service/send_message_instance'
import { eventTrackClient } from '../service/event_track_instance'
import { Node } from '../workflow/nodes/node'

export class TaskRegister {
  public static register() {
    SilentReAsk.registerTask(TaskName.leave_room, async (chat_id) => {
      const user_id = getUserId(chat_id)

      // 加锁来判断
      const lock = new AsyncLock()
      await lock.acquire(`liveRoomReminder_${chat_id}`, async () => {
        // 如果还在线，退出
        if ((await chatStateStoreClient.getFlags<IChattingFlag>(chat_id)).is_in_live_room || !await isInClassTime(chat_id)) {
          return
        }
        // 如果没上课30分钟，就不发
        if (dayjs().isBefore(dayjs().hour(19).minute(20))) {
          return
        }

        const limiter = new RateLimiter({ // 30 分钟内只允许一次解读
          windowSize: 30 * 60,
          maxRequests: 1
        })
        const currentTime = await DataService.getCurrentTime(chat_id)
        const nodeName = `leave_room_notify_day${currentTime.day}`

        const isAllowed = await limiter.isAllowed(nodeName, chat_id)

        if (!isAllowed) {
          return
        }

        await chatStateStoreClient.increaseNodeCount(chat_id, nodeName)
        const nodeInvokeCount = await chatStateStoreClient.getNodeCount(chat_id, nodeName)
        const maxNotifyCount = 2
        if (nodeInvokeCount > maxNotifyCount) {
          return
        }

        const state = await getState(chat_id, user_id)
        const context = await ContextBuilder.build({
          state,
          customerChatRounds: 2,
          customPrompt: '# 掉线提醒\n你只需要提醒客户他掉线了，直播课程很重要，赶快进来看中神通老师的直播课程，不要说别的',
        })

        await replyClient.invoke({
          state,
          maxTokens: 128,
          promptName: 'leave_room_notify',
          context: context,
          noSplit: true
        })
      })
    })

    // 注册商品点击提醒任务
    SilentReAsk.registerTask(TaskName.product_click, async (chat_id: string) => {
      const userId = getUserId(chat_id)
      // 加锁来判断
      const lock = new AsyncLock()
      await lock.acquire('productClickCheck', async () => {
        // 如果已经处理过下单失败，退出
        const limiterPayment = new RateLimiter({
          windowSize: 120 * 60,
          maxRequests: 2
        })
        const currentTime = await DataService.getCurrentTime(chat_id)
        const nodeName = `handled_product_click_day${currentTime.day}`
        const isAllowed = await limiterPayment.isAllowed(nodeName, chat_id)
        if (!isAllowed) { return }

        // 检查是否付款
        if (await DataService.isPaidSystemCourse(chat_id)) { return }

        // 通知客户
        await wecomMessageSender.sendById({
          chat_id: chat_id,
          user_id: userId,
          ai_msg: '看你点开了商品未下单，是遇到什么问题吗？'
        })
        await randomSleep(6000, 9000)
        await wecomMessageSender.sendById({
          chat_id: chat_id,
          user_id: userId,
          ai_msg: '那些赚到钱的老板拼的都是执行力，别再犹豫'
        })
        await randomSleep(6000, 9000)
        await wecomMessageSender.sendById({
          chat_id: chat_id,
          user_id: userId,
          ai_msg: '想都是问题，干就完了！你说对吗？'
        })

        // 通知人工客服
        eventTrackClient.track(chat_id, IEventType.PaymentNotCompleted)
        await HumanTransfer.transfer(chat_id, userId, HumanTransferType.HesitatePayment, 'onlyNotify')
      })
    })

    // 注册订单关闭提醒任务
    SilentReAsk.registerTask(TaskName.order_close, async (chat_id: string) => {
      const userId = getUserId(chat_id)
      // 加锁来判断
      const lock = new AsyncLock()
      await lock.acquire('orderCloseCheck', async () => {
        // 如果已经处理过下单失败，退出
        const limiterPayment = new RateLimiter({
          windowSize: 120 * 60,
          maxRequests: 2
        })
        const currentTime = await DataService.getCurrentTime(chat_id)
        const nodeName = `handled_order_close_day${currentTime.day}`
        const isAllowed = await limiterPayment.isAllowed(nodeName, chat_id)
        if (!isAllowed) { return }

        // 检查是否付款
        if (await DataService.isPaidSystemCourse(chat_id)) { return }

        // 通知客户
        await wecomMessageSender.sendById({
          chat_id: chat_id,
          user_id: userId,
          ai_msg: '看你下单了未付款，是遇到什么问题吗？'
        })
        await randomSleep(6000, 9000)
        await wecomMessageSender.sendById({
          chat_id: chat_id,
          user_id: userId,
          ai_msg: '名额有限，后台说不付款要踢掉给别人了'
        })
        await randomSleep(6000, 9000)
        await wecomMessageSender.sendById({
          chat_id: chat_id,
          user_id: userId,
          ai_msg: '别犹豫，赶紧付款！'
        })

        // 通知人工客服
        eventTrackClient.track(chat_id, IEventType.PaymentNotCompleted)
        await HumanTransfer.transfer(chat_id, userId, HumanTransferType.HesitatePayment, 'onlyNotify')
      })
    })

    // 注册作业清理任务
    SilentReAsk.registerTask(TaskName.homework_cleanup, async (chat_id: string, params) => {
      const { storeType } = params || {}
      if (storeType === 'homework1') {
        Homework1Store.clearUserMessages(chat_id)
      } else if (storeType === 'homework2') {
        Homework2Store.clearUserMessages(chat_id)
      }
    })

    // 注册手机号检查任务
    SilentReAsk.registerTask(TaskName.phone_check, async (chat_id: string) => {
      const userId = getUserId(chat_id)

      const phoneNumber = await DataService.bindPhoneFromRemark(chat_id)
      await chatStateStoreClient.update(chat_id, {
        nextStage:Node.IntentionQuery
      })
      if (!phoneNumber) {
        await HumanTransfer.transfer(chat_id, userId, HumanTransferType.NotBindPhone, 'onlyNotify')
        // 没回手机号，继续走挖需
        await startAskIntention(chat_id, userId)
      }
    })

    // 注册询问意图任务
    SilentReAsk.registerTask(TaskName.ask_intention, async (chat_id: string) => {
      const userId = getUserId(chat_id)
      await firstAskIntention(chat_id, userId)
    })

    // 注册询问意图提醒任务
    SilentReAsk.registerTask(TaskName.ask_intention_reminder, async (chat_id: string) => {
      const userId = getUserId(chat_id)

      const isUserReply = await chatHistoryServiceClient.isLastMessageWithDuration(chat_id, 999, 'minute')
      if (isUserReply) return

      await wecomMessageSender.sendById({
        chat_id: chat_id,
        user_id: userId,
        ai_msg: '同学，你忙完了吗？看你一个小时没回复我了。麻烦抽空填写下我发给你的信息表，这样老师可以更好地帮你诊断问题哦～'
      },
      { shortDes: '要求客户填写信息表' })
    })

    // 注册催促发送抖音截图任务
    SilentReAsk.registerTask(TaskName.urge_douyin_screenshot, async (chat_id: string) => {
      const userId = getUserId(chat_id)

      if (await DataService.isDoingDouyin(chat_id)) {
        await wecomMessageSender.sendById({
          chat_id: chat_id,
          user_id: userId,
          ai_msg: '同学你抖音截图先发我一下，我看看你做成什么样了？'
        },
        { shortDes: '第一天催促客户发送抖音截图' })
      }

      // 再调度引导课程任务
      await SilentReAsk.schedule(
        TaskName.guide_lesson,
        chat_id,
        1000 * 60 * 10, // 10分钟后执行，给抖音截图任务留出时间
        undefined,
        { auto_retry: true, independent:true }
      )
    })

    // 注册执行延期开课任务
    SilentReAsk.registerTask(TaskName.enter_postpone, async (chat_id: string) => {
      await chatStateStoreClient.update(chat_id, {
        state: <IChattingFlag> {
          is_in_postpone: false,
        },
      })
      //重启sop
      await chatDBClient.setStopGroupPush(chat_id, false)
    })

    // 注册引导课程任务
    SilentReAsk.registerTask(TaskName.guide_lesson, async (chat_id: string) => {
      const userId = getUserId(chat_id)

      const userName = await DataService.getUserName(chat_id)
      await wecomMessageSender.sendById({
        user_id: userId,
        chat_id: chat_id,
        ai_msg:`${userName}，今天你所说出来的问题明天晚上18：50中神通老师都会讲到，
咱们也不用过于担心，只要咱们认真学习，肯定是可以解决掉的，咱们就放宽心，跟着老师学习就好了`
      })
    })

    // 注册改日重新询问任务
    SilentReAsk.registerTask(TaskName.reask_another_day, async (chat_id: string) => {
      const userId = getUserId(chat_id)
      const userName = await DataService.getUserName(chat_id)
      const isPaid = await DataService.isPaidSystemCourse(chat_id)
      if (!isPaid) {
        // const state = await getState(chat_id, userId)
        // const context = await YuHeContextManager.build({
        //   state,
        //   chatHistoryRounds: 6,
        //   customPrompt: '# 次日提醒\n客户昨天说今天要下单，你要根据上下文询问客户考虑的怎么样了，名额不能再继续保留了，不要说别的',
        // })
        //
        // await YuHeReply.invoke({
        //   state,
        //   maxTokens: 100,
        //   promptName: 'reask_another_day',
        //   context: context,
        //   noSplit: true
        // })
        const reaskMessage = [
          `${userName}[捂脸]给你留的陪跑营名额再不报名就要失效了！跟你同期报名的学员已经在陪跑老师的指导下风风火火干起来了！用AI做抖音已经成为老板们的一波赚钱风口，咱可别错过！`,
          '我把报名链接发给你，咱把报名手续办了？💪']
        await wecomCommonMessageSender.sendMsg(userId, reaskMessage.map((item) => {
          return {
            type: SendMessageType.text,
            text: item,
            description: item
          }
        }), {})
        await chatStateStoreClient.update(chat_id, { state: { is_before_payment: true } })
        await HumanTransfer.transfer(chat_id, userId, HumanTransferType.ReaskAnotherDay, 'onlyNotify', `${Config.setting.AGENT_NAME}：${reaskMessage.join('\n')}`)
      }
    })

    // 注册测试任务
    SilentReAsk.registerTask(TaskName.test_task, async (chat_id: string, params) => {
      logger.log(`执行测试任务 for chat: ${chat_id}`, params)
    })
  }
}