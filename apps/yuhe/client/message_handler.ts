import { getChatId, getUserId } from 'config/chat_id'
import { LLM } from 'lib/ai/llm/llm_model'
import { HumanTransfer, HumanTransferType } from '../human_transfer/human_transfer'
import { IEventType } from 'model/logger/data_driven'
import { ObjectUtil } from 'lib/object'
import { IReceivedMessage, IWecomReceivedMsgType } from 'model/juzi/type'
import logger from 'model/logger/logger'
import { Config } from 'config'
import { AliyunCredentials } from 'lib/cer'
import OpenAI from 'openai'
import axios from 'axios'
import { IChattingFlag } from '../state/user_flags'
import { sendYuHeWelComeMessage } from './event_handler'
import { chatStateStoreClient } from '../service/base_instance'
import { eventTrackClient } from '../service/event_track_instance'

const MAX_VIDEO_SIZE = 150 * 1024 * 1024 // 150MB
const MAX_VIDEO_DUATION = 40 // 秒

// Initialize Aliyun credentials
AliyunCredentials.initialize({
  region: 'cn-hangzhou',
  accountId: '****************',
  accessKeyId: 'LTAI5tRVPxefUtgyLCfc5f69',
  secretAccessKey: '******************************',
})

export async function handleImageMessage(imageUrl: string, chatId: string) {
  const response = await new LLM({
    maxTokens: 200,
    meta: { promptName: 'image_caption', chat_id: chatId, description: '图片转文本' }
  }).imageChat(imageUrl, `# 图片描述
你是一名抖音流量课导师，正与客户聊天。客户发来一张图片，请先分类图片，然后用一段话解释图片内容

## 分类规则
1. 社交媒体主要是抖音，快手，微信视频号和小红书这四大社交媒体平台），满足以下2项即可认定为社交媒体首页截图：
  - 同一水平行出现“获赞”“关注”“粉丝”三个中文词
  - 有以“作品”“视频”“笔记”开头的分页标签，其下方紧跟多张视频缩略图栅格，或空白（没有作品）
2. 其他情况均视为普通图片

## 输出格式
- 若为普通图片，文本以“【普通图片】”开头，然后正常描述图片内容，不需要明确声明这不是社交媒体首页截图
- 若为社交媒体首页截图，文本以“【社交媒体首页截图】”开头，并描述以下内容：头像，名称，背景，获赞，关注，粉丝，作品描述，作品获赞等信息`)

  eventTrackClient.track(chatId, IEventType.TransferToManual, { reason: ObjectUtil.enumValueToKey(HumanTransferType, HumanTransferType.UnknownMessageType),
    image_url: imageUrl, msg_type: ObjectUtil.enumValueToKey(IWecomReceivedMsgType, IWecomReceivedMsgType.Image) })
  return `${response}`
}

export async function getVideoFileSize(url: string) {
  try {
    const res = await axios.head(url, { timeout: 5000 })
    const length = res.headers['content-length']
    return length ? parseInt(length, 10) : null
  } catch (error) {
    logger.warn(`HEAD 请求失败，无法获取视频文件大小: ${error}`)
    return null
  }
}

export async function handleVideoMessage(videoUrl: string, chatId: string) {
  const userId = getUserId(chatId)
  const dashscopeApiKey = Config.setting.qwen.apiKey || process.env.DASHSCOPE_API_KEY

  const openai = new OpenAI({
    apiKey: dashscopeApiKey,
    baseURL: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
  })

  try {
    const sizeBytes = await getVideoFileSize(videoUrl)
    if (sizeBytes !== null && sizeBytes > MAX_VIDEO_SIZE) {
      throw new Error(`视频大小 ${ (sizeBytes / (1024 * 1024)).toFixed(2) } MB 超过 150 MB 限制`)
    }
    const messages: any = [
      {
        'role': 'user',
        'content': [{
          'type': 'video_url',
          'video_url': { 'url': videoUrl },
        },
        { 'type': 'text', 'text': '请以【视频】开头，然后分析视频的内容是什么，输出一段话，请不要使用markdown格式' }]
      }]
    const qwenResponse = await openai.chat.completions.create({
      model: 'qwen-omni-turbo',
      messages: messages,
      max_completion_tokens: 512,
      stream: true,
      stream_options: {
        include_usage: true
      },
      modalities: ['text']
    })
    let qwenResponseText = ''
    for await (const chunk of qwenResponse) {
      qwenResponseText += chunk.choices[0]?.delta.content || ''
    }
    qwenResponseText = qwenResponseText.trim()
    if (!qwenResponseText.startsWith('【视频】')) { qwenResponseText = `【视频】${qwenResponseText}` }
    qwenResponseText = qwenResponseText.replace(/\n/g, '')

    // await YuHeHumanTransfer.transfer(chatId, userId, YuHeHumanTransferType.ProcessVideo, 'onlyNotify', qwenResponseText)
    eventTrackClient.track(chatId, IEventType.TransferToManual, {
      reason: ObjectUtil.enumValueToKey(
        HumanTransferType,
        HumanTransferType.UnknownMessageType
      ),
      video_url: videoUrl,
      msg_type: ObjectUtil.enumValueToKey(
        IWecomReceivedMsgType,
        IWecomReceivedMsgType.Video
      ),
    })

    return qwenResponseText
  } catch (error) {
    logger.warn(`处理视频消息时出错: ${error}`)
    await HumanTransfer.transfer(chatId, userId, HumanTransferType.ProcessVideoFailed, true)
    eventTrackClient.track(chatId, IEventType.TransferToManual, {
      reason: ObjectUtil.enumValueToKey(HumanTransferType, HumanTransferType.UnknownMessageType),
      video_url: videoUrl,
      msg_type: ObjectUtil.enumValueToKey(IWecomReceivedMsgType, IWecomReceivedMsgType.Video),
    })
    return ''
  }
}

export async function handleUnknownMessage(message: IReceivedMessage) {
  if (!message.imContactId) { return }

  const chat_id = getChatId(message.imContactId)
  await HumanTransfer.transfer(chat_id, message.imContactId, HumanTransferType.UnknownMessageType)

  eventTrackClient.track(chat_id,
    IEventType.TransferToManual,
    { reason: ObjectUtil.enumValueToKey(HumanTransferType,  HumanTransferType.UnknownMessageType),
      message: JSON.stringify(message), msg_type: ObjectUtil.enumValueToKey(IWecomReceivedMsgType, message.messageType)
    })

  logger.warn(`未知消息，请及时处理：${message.messageType}`)
}


// 处理微信欢迎语
export async function handleWelcomeMessage(userId: string) {
  const chatId = getChatId(userId)
  const flags = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
  if (flags.is_friend_accepted) {
    return
  }

  // 如果没有通过好友事件的话，进入欢迎语流程
  await sendYuHeWelComeMessage(getChatId(userId), userId)
}