import { IWorkflowState } from 'service/llm/state'
import { Node, trackInvoke, WorkFlowNode } from './node'
import { ChatState } from 'service/local_cache/chat_state'
import { SilentReAsk } from 'service/schedule/silent_requestion'
import { TaskName } from '../../schedule/type'
import { Config } from 'config'
import { sleep } from 'lib/schedule/schedule'
import logger from 'model/logger/logger'
import { StringHelper } from 'lib/string'
import { IEventType } from 'model/logger/data_driven'
import { ContextBuilder } from '../context'
import { replyClient } from '../../service/instance'
import { chatStateStoreClient } from '../../service/base_instance'
import { eventTrackClient } from '../../service/event_track_instance'

export class Homework1Node extends WorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState):Promise<Node> {
    Homework1Store.addUserMessage(state.chat_id, state.userMessage)

    // 加个兜底逻辑，5 分钟后去把队列清空一下
    await SilentReAsk.schedule(
      TaskName.homework_cleanup,
      state.chat_id,
      5 * 60 * 1000,
      { storeType: 'homework1' }
    )

    if (!Config.setting.localTest) {
      await sleep(3 * 60 * 1000)
    }

    const homework1Messages = Homework1Store.getUserMessages(state.chat_id)
    if (homework1Messages.indexOf(state.userMessage) !== homework1Messages.length - 1) { // 只有最后一条才进行处理，其他的当做打断处理
      logger.warn({ chat_id: state.chat_id }, '接收到新的Homework1消息，被合并到当前轮Homework1消息处理，本条消息不再做处理')
      return Node.FreeTalk
    }

    // 对客户作业消息进行合并处理， 并移除 比如👉：之前的字段
    const userHomework = homework1Messages.map((message) =>  StringHelper.removeStringBeforePrefix('👉：', message)).join('\n')

    // 清空 wealthOrchardMessages
    Homework1Store.clearUserMessages(state.chat_id)

    const context = await ContextBuilder.build({
      state,
      customerChatRounds: 0,
      customPrompt: `# 作业点评
- 客户听完第一节课的商业定位，给客户的作业做相应的点评，先根据4个字段的定义是否理解做基本对和错的判断，再根据账号定位、内容形式、人设定位、目标客户着四个方面一一指出客户的进步（给客户一定的学习信心，让其有一定的获得感的，让对方感受到课程价值）和不足之处
- 先鼓励客户，基于帮助客户在思路上完成抖音获客的目标，根据实现目标的路径：定位清晰，基于定位生产有流量和转化能力的视频。引导铺垫到下一节课具体怎么生产爆款视频的学习的重要性！注意不要一味提供作业解答服务，细节落地问题引导客户去上课，老师会讲的。不要主动提及细节问题，我们只主动提供方向思路即可

## 作业模版
【第一课作业】
商业定位：（变现路径、客户在抖音怎么获客，直播引流、短视频、引流,团购。达人探店，短视频引流，图文引流等等）
内容定位：（什么方式拍摄：老师在课程有提到剧情+行业，口播+行业,图文+行业，纯行业，纯口播。纯行业纯项目）
人设定位：（希望给别人留下什么印象，例如真诚，专业，实在，实力等。让客户有好感的人设等）
目标客户：（同城客户还是全国客户）
你想一个月通过抖音赚多少钱：
【用老师发的模版填写，收到后我会1对1给您反馈】

## 示例1
客户作业
  商业定位：皮肤管理门店
  内容定位：客户变美案例分享+设备操作展示
  人设定位：专业美容师
  目标客户：想改善肤质的白领女性
  你想一个月通过抖音赚多少钱：3万
思考过程：整体定位比较清晰，内容上属于常规展示类，缺少钩子和故事结构；人设稳妥但略显中规中矩；目标客户明确，变现目标合理但缺执行手段；在评判客户作业的时候尽量只指出一个大的方向，而不是向客户输出很多专业名词客户接受不了，整体思路是我要给客户一个赞同说 她现在已经有了大体的思路了，学的不错，后面用50字的话语将客户引导到明天的课，需要用Day2课程的“爆款模版+AI批量拍”进行突破，让客户认识到跟着上课学习才能赚钱
输出结果：你这套定位非常扎实，尤其目标客户和变现目标都很明确👍内容形式也有思路，说明跟着中神通老师学习还是有不错效果的，但目前展示类内容传播力有限，容易陷入“自嗨式”输出。人设虽然专业，但还不够“吸粉”，要想做到3万/月，一定得用上Day2讲的爆款模版结构和钩子设计再加上AI数字人，快速做出能“吸引+转化”的内容，你现在只是学习到了点思路，但是很多逻辑你还不清楚，学习清楚才能知道怎么做事情，这才是转化破局关键！建议第二节课认真听，真的非常关键，想赚钱就跟着中神通老师学习，一定要学深，学透才能赚到钱

## 示例2
客户作业
  商业定位：高端月子中心
  内容定位：环境展示+客户见证
  人设定位：亲和力强的妈妈运营者
  目标客户：孕期或产后修复女性
  你想一个月通过抖音赚多少钱：6万
思考过程：账号定位很清晰，具备品牌感，但内容形式偏保守；人设思路好但标签不突出；目标客户抓得准，我需要评价客户现在听完中神通老师的课程已经有了大体的方向，对客户作业的评价要颠倒为止不用太细化和太多专业名词，剩下一半的内容我需要引导客户上第二节课，里面很多的知识更深入理解现在的逻辑， 通过Day2课程建立矩阵打法、复制爆款内容加速变现，让客户一步步听课，对于客户day1的作业诊断更加结构化一些诊断+引导上个各占一半
输出结果：你要是想月入6万，光靠自然流量远远不够，需要跟着中神通老师更加深入的学习，day2课程会教你如何做超级门店批量复制爆款视频，打造适合你的账号，这才是高客单客户最该掌握的底层方法，你需要踏踏实实认真学习，而不是一知半解的就把听到的内容就认为是自己的了，要是这样的话你早发财了，还是安心跟着中神通老师学习，你现在走的每一小步后面都能变成一大步，学的扎实每一步都理解才能够用在自己赚钱上了，还是安心跟着中神通老师学习，你现在走的每一小步后面都能变成一大步，才能赚到更多的钱！

## 输出要求
- 输出一段话，口语化表达，结果尽量保持在200字以内
- 直接输出回答客户的结果，不要输出思考过程或其他内容
- 如果客户发言是作业内容，则按照要求点评，反之就正常回复客户

## 客户作业
${userHomework}`,
    })

    await replyClient.invoke({
      state,
      context: context,
      promptName: 'homework1',
      noInterrupt: true,
      noSplit: true,
    })
    eventTrackClient.track(state.chat_id, IEventType.HomeworkComplete, { 'day': 1 })
    await chatStateStoreClient.update(state.chat_id, { state:{ is_complete_homework1: true } })
    return Node.FreeTalk
  }
}

export class Homework1Store {
  private static userMessages = new ChatState<string[]>()  // 对客户的消息进行暂存

  public static addUserMessage(chat_id: string, message: string) {
    const messages = this.userMessages.get(chat_id) || []
    messages.push(message)
    this.userMessages.set(chat_id, messages)
  }

  public static getUserMessages(chat_id: string) {
    return this.userMessages.get(chat_id) || []
  }

  public static clearUserMessages(chat_id: string) {
    this.userMessages.set(chat_id, [])
  }
}