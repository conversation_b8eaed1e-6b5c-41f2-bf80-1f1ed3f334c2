import { ISOPTask } from 'service/visualized_sop/visualized_sop_type'
import Task from './task'

export async function UserExistSop({ queryExistSopByChatId, chatId } : {queryExistSopByChatId(chatId:string):Promise<ISOPTask[]>, chatId:string}) {
  const jobs = await queryExistSopByChatId(chatId)
  return <div>
    <table className="table">
      <thead>
        <tr>
          <th>name</th>
          <th>week</th>
          <th>day</th>
          <th>time</th>
          <th>sendTime</th>
          <th>force</th>
          <th>action</th>
        </tr>
      </thead>
      <tbody>
        {jobs.map((item, index) => <Task key={index} task={item}/>)}
      </tbody>
    </table>
  </div>
}