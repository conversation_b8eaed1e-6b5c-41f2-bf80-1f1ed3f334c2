'use server'

import axios from 'axios'
import dayjs from 'dayjs'
import { AdminPrismaMongoClient } from '@/lib/prisma'
import { Config } from 'config'
import { HaoguUserData } from '../type/user'
import { Node } from 'haogu/src/workflow/node'
import { UUID } from 'lib/uuid/uuid'
import { getUserId } from 'config/chat_id'
import { HWLink } from 'haogu/src/config/override'

Config.setting.projectName = 'haogu'

export async function queryChats(nameOrPhone:string, courseNo?:number):Promise<HaoguUserData[]> {
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  const queryNameResult = await mongoClient.chat.findMany({
    take:200,
    orderBy:{
      created_at:'desc'
    },
    where:{
      AND:[
        {
          course_no:courseNo,
        },
        {
          OR:[
            { phone:{ contains:nameOrPhone } },
            { id:nameOr<PERSON><PERSON> },
            { contact:{
              is:{
                wx_name: {
                  contains: nameOr<PERSON><PERSON>
                }
              }
            }
            }
          ]
        }
      ]
    } })
  return queryNameResult as unknown as HaoguUserData[]
}

export async function queryDefaultChats() {
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  const queryNameResult = await mongoClient.chat.findMany({
    take:10,
    orderBy: {
      created_at: 'desc' // 按照 create_at 降序排列
    },
  })
  return queryNameResult as unknown as HaoguUserData[]
}

export async function queryChatById(id:string):Promise<HaoguUserData | null> {
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  const result = await mongoClient.chat.findFirst({ where:{
    id
  } })
  return result as unknown as (HaoguUserData | null)
}

export async function queryChatsWithoutAi(courseNo?:number): Promise<HaoguUserData[]> {
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  const result = await mongoClient.chat.findMany({ where:{
    is_human_involved:true,
    course_no:courseNo
  },
  take: courseNo ? undefined : 50
  })
  return result as unknown as HaoguUserData[]
}

export async function queryChatsWithoutPhone(courseNo?:number):Promise<HaoguUserData[]> {
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  const result = await mongoClient.chat.findRaw({ filter: { phone: { $exists: false }, course_no: courseNo }, options:{
    limit: courseNo ? undefined : 50
  } }) as unknown as any[]
  for (let i = 0; i < result.length; i++) {
    result[i].id = result[i]['_id']
  }
  return result
}

export async function changeIsHumanInvolved(chatId:string, isHumanInvolved:boolean) {
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  await mongoClient.chat.update({ where:{ id:chatId }, data: { is_human_involved:isHumanInvolved } })
}

export async function changeIsStopGroupPush(chatId:string, isStopGroupPush:boolean) {
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  await mongoClient.chat.update({ where:{ id:chatId }, data: { is_stop_group_push:isStopGroupPush } })
}

export async function changeCourseNo(chatId:string, courseNo:number) {
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  await mongoClient.chat.update({ where:{ id:chatId }, data:{ course_no:courseNo } })
}

export async function changeNextStage(chatId:string, stage:Node) {
  'use server'
  const mongoClient = AdminPrismaMongoClient.getHaoguInstance()
  const chatInfo = await mongoClient.chat.findFirst({ where: { id: chatId } })
  if (!chatInfo) {
    throw '没有找到这个人'
  }

  // 直接使用原生命令更新嵌套字段 chat_state.nextState
  await mongoClient.$runCommandRaw({
    update: 'chat',
    updates: [
      {
        q: { _id: chatId },
        u: { $set: { 'chat_state.nextState': stage } }
      }
    ]
  })

  await clearCache(chatId)
}

export async function clearCache(chatId:string):Promise<void> {
  'use server'
  const mongoClient = AdminPrismaMongoClient.getHaoguInstance()
  const chatInfo = await mongoClient.chat.findFirst({ where:{ id:chatId } })
  if (!chatInfo) {
    throw '没有找到这个人'
  }
  const mongoConfigInstance = AdminPrismaMongoClient.getConfigInstance()
  const botInfo = await mongoConfigInstance.config.findFirst({ select:{ address:true }, where:{ wechatId:chatInfo.wx_id } })
  if (!botInfo) {
    throw '没有找到对应的机器人配置'
  }
  const address = botInfo.address
  await axios(`${address}/test/event/clear_cache`, {
    method:'POST',
    data:{
      chat_id: chatId,
    }
  }).then((res) => {
    if (res.data.code != 200) {
      throw res.data.msg
    }
  })
}

export async function getChatByCourseWeekRange(
  minCourseWeek: number,
  maxCourseWeek: number
) {
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  const chatList = await mongoClient.chat.findMany({
    where: {
      course_no: {
        gte: minCourseWeek,
        lte: maxCourseWeek,
      },
    },
  })
  return chatList
}

export async function updateIsPaid(chatId:string, isPaid:boolean): Promise<void> {
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  await mongoClient.$runCommandRaw({
    update: 'chat', // 集合名，注意区分大小写
    updates: [
      {
        q: { _id: chatId }, // 查询条件
        u: { $set: { 'chat_state.state.is_complete_payment': isPaid } }
      }
    ]
  })
  await clearCache(chatId)
}

export async function updateIsAttendCourse(chatId:string, day:number, isAttend:boolean):Promise<void> {
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  await mongoClient.$runCommandRaw({
    update: 'chat', // 集合名，注意区分大小写
    updates: [
      {
        q: { _id: chatId }, // 查询条件
        u: { $set: { [`chat_state.state.is_attend_course_day${day}`]: isAttend } }
      }
    ]
  })

  await clearCache(chatId)
}

export async function updateIsCompleteCourse(chatId:string, day:number, isAttend:boolean):Promise<void> {
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  if (isAttend) {
    await mongoClient.$runCommandRaw({
      update: 'chat', // 集合名，注意区分大小写
      updates: [
        {
          q: { _id: chatId }, // 查询条件
          u: { $set: {
            [`chat_state.state.is_complete_course_day${day}`]: isAttend,
            [`chat_state.state.is_attend_course_day${day}`]: isAttend,
          } }
        }
      ]
    })
  } else {
    await mongoClient.$runCommandRaw({
      update: 'chat', // 集合名，注意区分大小写
      updates: [
        {
          q: { _id: chatId }, // 查询条件
          u: { $set: { [`chat_state.state.is_complete_course_day${day}`]: isAttend } }
        }
      ]
    })

  }

  await clearCache(chatId)
}

export async function sendOrderEvent(chatId:string) {
  'use server'
  const mongoClient = AdminPrismaMongoClient.getHaoguInstance()
  const chatInfo = await mongoClient.chat.findFirst({ where:{ id:chatId } })
  if (!chatInfo) {
    throw '没有找到这个人'
  }
  const mongoConfigInstance = AdminPrismaMongoClient.getConfigInstance()
  const botInfo = await mongoConfigInstance.config.findFirst({ select:{ address:true }, where:{ wechatId:chatInfo.wx_id } })
  if (!botInfo) {
    throw '没有找到对应的机器人配置'
  }
  const address = botInfo.address
  await axios(`${address}/order`, {
    method:'POST',
    data:{
      chat_id: chatId,
      orderId: UUID.v4(),
      unifiedUserId:getUserId(chatId),
      createDate:new Date().getTime()
    }
  }).then((res) => {
    if (res.data.code != 200) {
      throw res.data.msg
    }
  })
}

export async function sendFinishHomeworkEvent(chatId:string, day:number, score:number) {
  'use server'
  const mongoClient = AdminPrismaMongoClient.getHaoguInstance()
  const chatInfo = await mongoClient.chat.findFirst({ where:{ id:chatId } })
  if (!chatInfo) {
    throw '没有找到这个人'
  }
  const mongoConfigInstance = AdminPrismaMongoClient.getConfigInstance()
  const botInfo = await mongoConfigInstance.config.findFirst({ select:{ address:true }, where:{ wechatId:chatInfo.wx_id } })
  if (!botInfo) {
    throw '没有找到对应的机器人配置'
  }
  const address = botInfo.address
  let link = ''
  if (day == 1) {
    link = HWLink.day1
  } else if (day == 2) {
    link = HWLink.day2
  } else if (day == 3) {
    link = HWLink.day3
  } else if (day == 4) {
    link = HWLink.day4
  } else if (day == 5) {
    link = HWLink.day5
  } else if (day == 6) {
    link = HWLink.day6
  } else {
    throw ('day is wrong')
  }
  await axios(`${address}/finish_work`, {
    method:'POST',
    data:{
      chat_id: chatId,
      custUnifiedUserId:getUserId(chatId),
      link,
      status:1,
      score
    }
  }).then((res) => {
    if (res.data.code != 200) {
      throw res.data.msg
    }
  })

}

export async function updatePayTime(chatId:string, time:string): Promise<void> {
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  await mongoClient.chat.update({ where:{ id:chatId }, data:{ pay_time:dayjs(time).toDate() } })
}