'use client'
import { useState } from 'react'
import { toast } from 'react-toastify'
import { HaoguUserData } from '../../type/user'
import { IChattingFlag } from 'haogu/src/config/manifest'

export function ChatEdit<T>({
  chat,
  changeCourseNo,
  changeNextStage,
  stageOption,
  updateIsPaid,
  clearCache,
  resetSop,
  updateIsAttendCourse,
  updateIsCompleteCourse,
  sendFinishHomeworkEvent,
  sendOrderEvent
}: {
  chat:HaoguUserData,
  changeNextStage(chatId: string, stage: T): Promise<void>,
  changeCourseNo(chatId: string, courseNo: number): Promise<void>,
  clearCache(chatId:string):Promise<void>
  updateIsPaid(chatId:string, isPaid:boolean): Promise<void>
  resetSop(chatId:string): Promise<void>
  updateIsAttendCourse(chatId: string, day: number, isAttend: boolean): Promise<void>
  updateIsCompleteCourse(chatId: string, day: number, isAttend: boolean): Promise<void>
  sendOrderEvent(chatId: string): Promise<void>
  sendFinishHomeworkEvent(chatId: string, day: number, score: number): Promise<void>
  stageOption:string[]
}) {
  const [stage, setStage] = useState<string>(chat.chat_state.nextStage)
  const [courseNo, setCourseNo] = useState<string> (`${chat.course_no ?? ''}`)
  const [isPaid, setIsPaid] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_complete_payment ?? false)
  const [loading, setLoading] = useState<boolean>(false)
  const [isAttendCourseDay1, setIsAttendCourseDay1] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_attend_course_day1 ?? false)
  const [isAttendCourseDay2, setIsAttendCourseDay2] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_attend_course_day2 ?? false)
  const [isAttendCourseDay3, setIsAttendCourseDay3] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_attend_course_day3 ?? false)
  const [isAttendCourseDay4, setIsAttendCourseDay4] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_attend_course_day4 ?? false)
  const [isAttendCourseDay5, setIsAttendCourseDay5] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_attend_course_day5 ?? false)
  const [isAttendCourseDay6, setIsAttendCourseDay6] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_attend_course_day6 ?? false)
  const [isCompleteCourseDay1, setIsCompleteCourseDay1] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_complete_course_day1 ?? false)
  const [isCompleteCourseDay2, setIsCompleteCourseDay2] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_complete_course_day2 ?? false)
  const [isCompleteCourseDay3, setIsCompleteCourseDay3] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_complete_course_day3 ?? false)
  const [isCompleteCourseDay4, setIsCompleteCourseDay4] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_complete_course_day4 ?? false)
  const [isCompleteCourseDay5, setIsCompleteCourseDay5] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_complete_course_day5 ?? false)
  const [isCompleteCourseDay6, setIsCompleteCourseDay6] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_complete_course_day6 ?? false)
  const [homeworkDay, setHomeworkDay] = useState<number>(1)
  const [homeworkScore, setHomeworkScore] = useState<number>(100)

  return <div>
    <div className='text-2xl p-2'>编辑: {chat.contact.wx_name}</div>
    <form className='flex gap-2 items-center' onSubmit={(e) => {
      e.preventDefault()
      setLoading(true)
      toast.promise(changeNextStage(chat.id, stage as T), {
        pending: 'change next stage pending',
        success: 'change next stage success',
        error: {
          render:(e) => {
            return `error: ${e.data}`
          }
        }
      }).finally(() => {
        setLoading(false)
      })
    }}>
      <label className='label w-40'>切换阶段</label>
      <select className='select focus-within:outline-0' disabled={loading} value={stage} onChange={(e) => {
        setStage(e.currentTarget.value)
      }}>
        {stageOption.map((item, index) => {
          return <option key={index}>{item}</option>
        })}
      </select>
      <button type="submit" className='btn disabled:btn-disabled' disabled={loading}>切换</button>
    </form>
    <form className='flex gap-2 items-center mt-2' onSubmit={(e) => {
      e.preventDefault()
      setLoading(true)
      toast.promise(changeCourseNo(chat.id, Number(courseNo)), {
        pending: 'change courseNo pending',
        success: 'change courseNo success',
        error: {
          render:(e) => {
            return `error: ${e.data}`
          }
        }
      }).finally(() => {
        setLoading(false)
      })
    }}>
      <label className='label w-40'>修改课程号</label>
      <input type="number" className='input focus-within:outline-0' value={courseNo} onChange={(e) => { setCourseNo(e.currentTarget.value) }}/>
      <button type="submit" className='btn disabled:btn-disabled' disabled={loading}>修改</button>
    </form>
    <div className='flex gap-2 items-center mt-2' >
      <label className='label w-40'>修改成单状态</label>
      <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isPaid} disabled={loading} onChange={(e) => {
        e.preventDefault()
        const checked = e.currentTarget.checked
        setLoading(true)
        toast.promise(async() => {
          await updateIsPaid(chat.id, e.currentTarget.checked)
          await clearCache(chat.id)
        }, {
          pending: 'change payment state pending',
          success: 'change payment state success',
          error: {
            render:(e) => {
              return `error: ${e.data}`
            }
          }
        }).then(() => {
          setIsPaid(checked)
        }).finally(() => {
          setLoading(false)
        })
      }}/>
    </div>
    <div className='flex gap-4'>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>修改第一天到课</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isAttendCourseDay1} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateIsAttendCourse(chat.id, 1, checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            setIsAttendCourseDay1(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>修改第一天完课</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isCompleteCourseDay1} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateIsCompleteCourse(chat.id, 1, checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            if (checked) {
              setIsAttendCourseDay1(checked)
            }
            setIsCompleteCourseDay1(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>

    </div>
    <div className='flex gap-4'>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>修改第二天到课</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isAttendCourseDay2} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateIsAttendCourse(chat.id, 2, checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            setIsAttendCourseDay2(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>修改第二天完课</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isCompleteCourseDay2} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateIsCompleteCourse(chat.id, 2, checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            if (checked) {
              setIsAttendCourseDay2(checked)
            }
            setIsCompleteCourseDay2(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>

    </div>
    <div className='flex gap-4'>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>修改第三天到课</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isAttendCourseDay3} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateIsAttendCourse(chat.id, 3, checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            setIsAttendCourseDay3(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>修改第三天完课</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isCompleteCourseDay3} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateIsCompleteCourse(chat.id, 3, checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            if (checked) {
              setIsAttendCourseDay3(checked)
            }
            setIsCompleteCourseDay3(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>

    </div>
    <div className='flex gap-4'>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>修改第四天到课</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isAttendCourseDay4} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateIsAttendCourse(chat.id, 4, checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            setIsAttendCourseDay4(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>修改第四天完课</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isCompleteCourseDay4} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateIsCompleteCourse(chat.id, 4, checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            if (checked) {
              setIsAttendCourseDay4(checked)
            }
            setIsCompleteCourseDay4(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>

    </div>
    <div className='flex gap-4'>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>修改第五天到课</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isAttendCourseDay5} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateIsAttendCourse(chat.id, 5, checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            setIsAttendCourseDay5(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>修改第五天完课</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isCompleteCourseDay5} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateIsCompleteCourse(chat.id, 5, checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            if (checked) {
              setIsAttendCourseDay5(checked)
            }
            setIsCompleteCourseDay5(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>

    </div>
    <div className='flex gap-4'>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>修改第六天到课</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isAttendCourseDay6} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateIsAttendCourse(chat.id, 6, checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            setIsAttendCourseDay6(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>修改第六天完课</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isCompleteCourseDay6} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateIsCompleteCourse(chat.id, 6, checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            if (checked) {
              setIsAttendCourseDay6(checked)
            }
            setIsCompleteCourseDay6(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>

    </div>
    <div className='mt-2 flex items-center gap-4'>
      <div className='flex items-center gap-2'>
        <div className='flex gap-2 items-center'>
          <label htmlFor="" className='label'>day</label>
          <input type="number" className='input w-20' max={6} min={1} step={1} value={homeworkDay} onChange={(e) => {
            setHomeworkDay(e.currentTarget.valueAsNumber)
          }} />
        </div>
        <div className='flex gap-2 items-center'>
          <label htmlFor="" className='label'>score</label>
          <input type="number" className='input w-20' max={100} min={0} step={1} value={homeworkScore} onChange={(e) => {
            setHomeworkScore(e.currentTarget.valueAsNumber)
          }} />
        </div>
      </div>
      <button className='btn btn-neutral focus-within:outline-0 disabled:btn-disabled' disabled={loading} onClick={() => {
        setLoading(true)
        toast.promise(sendFinishHomeworkEvent(chat.id, homeworkDay, homeworkScore), {
          pending: 'pending',
          success: 'success',
          error: 'error'
        }).finally(() => {
          setLoading(false)
        })
      }}>place an order</button>
    </div>
    <button className='btn btn-neutral focus-within:outline-0 disabled:btn-disabled' disabled={loading} onClick={() => {
      setLoading(true)
      toast.promise(sendOrderEvent(chat.id), {
        pending: 'pending',
        success: 'success',
        error: 'error'
      }).finally(() => {
        setLoading(false)
      })
    }}>place an order</button>
    <div className='flex gap-2 mt-2'>
      <button className='btn btn-neutral focus-within:outline-0 disabled:btn-disabled' disabled={loading} onClick={() => {
        setLoading(true)
        toast.promise(clearCache(chat.id), {
          pending: 'clear pending',
          success: 'clear success',
          error: 'clear error'
        }).finally(() => {
          setLoading(false)
        })
      }}>clear cache</button>
      <button className='btn btn-neutral focus-within:outline-0 disabled:btn-disabled' disabled={loading} onClick={() => {
        setLoading(true)
        toast.promise(resetSop(chat.id), {
          pending: 'reset pending',
          success: 'reset success',
          error: 'reset error'
        }).finally(() => {
          setLoading(false)
        })
      }}>reset sop</button>
    </div>
  </div>
}