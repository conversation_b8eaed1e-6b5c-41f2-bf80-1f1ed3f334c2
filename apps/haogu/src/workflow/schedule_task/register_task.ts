import { SilentReAsk } from 'service/schedule/silent_requestion'
import logger from 'model/logger/logger'
import { chatStateStoreClient } from '../config/instance/base_instance'
import { commonMessageSender } from '../config/instance/send_message_instance'
import { IChattingFlag } from '../config/manifest'

export enum TaskName {
  test_task = 'test_task',
  send_666_follow_up = 'send_666_follow_up', // 发送666后续消息
  send_888_follow_up = 'send_888_follow_up', // 发送888后续消息
}


/**
 * 延迟任务
 */
export class TaskRegister {
  public static register() {
    // 注册测试任务
    SilentReAsk.registerTask(TaskName.test_task, async (chat_id: string, params) => {
      logger.log(`执行测试任务 for chat: ${chat_id}`, params)
    })

    // 注册666后续消息任务
    SilentReAsk.registerTask(TaskName.send_666_follow_up, async (chat_id: string, params) => {
      const state = await chatStateStoreClient.getFlags<IChattingFlag>(chat_id)

      // 如果已经发送过，则不再发送
      if (state.is_send_666_follow_up_message) {
        logger.log(`666后续消息已发送，跳过任务 for chat: ${chat_id}`)
        return
      }

      // 发送666后续消息
      await commonMessageSender.sendText(chat_id, {
        text: '筹码峰核心资料： \n【神奇的双线合一】a.ckjr001.com/xq4d3d/pe9joj3 （约7分钟） \n【绝密的四点共振】a.ckjr001.com/0NkyNm/pe9joj3（约8分钟） \n筹码买点战法看完回小楚【888】，我来教您安装和设置 无延迟筹码峰 。六节体系课程明天19：20开始。',
        description: '定时666后续消息'
      })

      // 更新状态
      await chatStateStoreClient.update(chat_id, {
        state: <IChattingFlag>{
          is_send_666_follow_up_message: true
        }
      })

      // 调度888后续消息任务（5分钟后）
      await SilentReAsk.schedule(
        TaskName.send_888_follow_up,
        chat_id,
        5 * 60 * 1000, // 5分钟
        undefined,
        { auto_retry: true, independent: false }
      )

      logger.log(`666后续消息已发送，并调度888任务 for chat: ${chat_id}`)
    })

    // 注册888后续消息任务
    SilentReAsk.registerTask(TaskName.send_888_follow_up, async (chat_id: string, params) => {
      const state = await chatStateStoreClient.getFlags<IChattingFlag>(chat_id)

      // 如果已经发送过，则不再发送
      if (state.is_send_888_follow_up_message) {
        logger.log(`888后续消息已发送，跳过任务 for chat: ${chat_id}`)
        return
      }

      // 发送888后续消息
      await commonMessageSender.sendText(chat_id, {
        text: '手机直接在平时下载软件的地方搜索【好人好股】下载就可以了哦 这是手机如何调出筹码峰视频：https://ai.9635.com.cn/hZRMmBbJ 不明白随时来问我哦\n' +
              '​\n' +
              '下面是电脑如何调视频有什么不明白的和小钱说 【先看视频哦】电脑筹码安装视频：https://ai.9635.com.cn/2T9ykocA 【电脑版】下载链接： https://www.9635.com.cn/web/jinrong/share',
        description: '定时888后续消息'
      })

      await commonMessageSender.sendText(chat_id, {
        text: '上面是安装方法，一定要看完视频才能调出无延迟筹码峰哦 调出筹码峰后告诉小钱，我教你如何利用筹码峰找卖点',
        description: '定时888后续消息'
      })

      // 更新状态
      await chatStateStoreClient.update(chat_id, {
        state: <IChattingFlag>{
          is_send_888_follow_up_message: true
        }
      })

      logger.log(`888后续消息已发送 for chat: ${chat_id}`)
    })
  }
}


