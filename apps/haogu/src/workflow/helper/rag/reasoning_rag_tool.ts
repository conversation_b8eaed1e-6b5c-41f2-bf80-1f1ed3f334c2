import { SimpleRag } from './simple_rag'
import ElasticSearchService from 'model/elastic_search/elastic_search'
import OpenAI from 'openai'
import { Config } from 'config'
import { RagPrompt } from './rag_prompt'
import { LLM } from 'lib/ai/llm/llm_model'


export interface IReasoningRagTool {
    name: string
    description: string
    execute: (params: RagToolExecuteParams) => Promise<string>
}

export interface RagToolExecuteParams {
    chatId: string
    roundId: string
    strategy: string
    searchKey?: string
}

export class ReasoningRagTool {

  public static GeneralSearch = 'rag搜索' //常规rag查询
  public static SearchStock = '查询股票信息' //查询股票详情
  public static SearchBaseRule = '搜索基础规则' //查询基础规则
  public static SearchTransactionSystemKnowledge = '搜索交易体系知识'


  private static client = new OpenAI({
    apiKey: Config.setting.qwen.apiKey,
    baseURL: 'https://dashscope.aliyuncs.com/compatible-mode/v1'
  })

  public static async getTools(): Promise<IReasoningRagTool[]> {
    return [
      await this.getGeneralSearchTool(),
      await this.getSearchStockTool(),
      await this.getSearchBaseRuleTool(),
      await this.getTransactionSystemKnowledgeTool(),
    ]
  }

  public static async getToolByKey(key: string): Promise<IReasoningRagTool | null> {
    const toolMap = {
      [ReasoningRagTool.GeneralSearch]: await this.getGeneralSearchTool(),
      [ReasoningRagTool.SearchStock]: await this.getSearchStockTool(),
      [ReasoningRagTool.SearchBaseRule]: await this.getSearchBaseRuleTool(),
      [ReasoningRagTool.SearchTransactionSystemKnowledge]: await this.getTransactionSystemKnowledgeTool(),
    }

    return Promise.resolve(toolMap[key])
  }


  private static async getGeneralSearchTool() {
    const name = ReasoningRagTool.GeneralSearch
    const description = `用于查询知识、课程与概念性问题。
- 输入格式要求：
1.input: {{ searchKey: "..." }}
2.searchKey 必须是 单一、清晰的自然语言问题（疑问句），以“？”结尾。
3.只能包含单一概念/问题，问题应尽量准确简短。`

    const execute = async (params: RagToolExecuteParams) => {
      // 获取要查询的文档
      const queryDocs = await SimpleRag.getQueryDocs(params.chatId)

      // 构建查询 filter
      const filter = {
        bool:{
          must:[
            {
              terms: {
                'metadata.doc': queryDocs
              }
            }
          ]
        }
      }

      const res = await ElasticSearchService.embeddingSearch(
        SimpleRag.index,
        params.searchKey ?? '',
        2,
        0.80,
        filter
      )

      if (res.length === 0) {
        return `${name}没有找到相关结果`
      } else {
        return `${name}搜索结果：
${res.map((item) => {
    return `问题：${item.metadata.q}
答案：${item.metadata.a}`
  }).join('\n')}`
      }
    }


    return {
      name:name,
      description: description,
      execute: execute
    } as IReasoningRagTool
  }


  private static async getSearchStockTool() {
    const name = ReasoningRagTool.SearchStock
    const description = '用于查询股票相关行情与动态，既可以是具体股票（如股票名称或代码），也可以是整体市场行情。入参：searchKey（股票名称、代码或市场描述）'

    const execute = async (params: RagToolExecuteParams) => {
      if (!params.searchKey) {
        return '参数searchKey为空'
      }
      const input = `请搜索下列股票近期涨跌情况并回答问题，输出中不要包含其他无关内容
${params.searchKey}`
      const response = await this.client.chat.completions.create({
        model: 'qwen-max',
        messages: [{
          role: 'user',
          content: input
        }],
        enable_search:true,
        search_options: {
          forced_search: true,
          enable_search_extension: true,
          search_strategy: 'max'
        }
      } as any)
      return response.choices[0].message.content
    }

    return {
      name:name,
      description: description,
      execute: execute
    } as IReasoningRagTool
  }


  private static async getSearchBaseRuleTool() {
    const name = ReasoningRagTool.SearchBaseRule
    const description = '提供基础聊天规则，不需要入参'

    const execute = async (params: RagToolExecuteParams) => {
      return `- 禁止提供任何手机号，微信号，物流单号，账户等敏感信息
- 明确课程都是直播，禁止说录播或录制等字眼`
    }

    return {
      name:name,
      description: description,
      execute: execute
    } as IReasoningRagTool
  }


  private static async getTransactionSystemKnowledgeTool(): Promise<IReasoningRagTool> {
    const name = ReasoningRagTool.SearchTransactionSystemKnowledge
    const description = '搜索交易体系知识，用于回复策略中需要向客户介绍交易体系的场景'


    const execute = async (params: RagToolExecuteParams): Promise<string> => {

      const prompt = await RagPrompt.extractTransactionSystemKnowledgePrompt(params.strategy)
      const response = await LLM.predict(prompt, { meta: { round_id: params.roundId }, responseJSON: true })

      // console.log('executeTransactionSystemKnowledge', response)

      const jsonData = JSON.parse(response)
      return jsonData.knowledge
    }

    return {
      name:name,
      description: description,
      execute: execute
    } as IReasoningRagTool
  }




}